Deliverable 1 – Scope of Works 1: Founda on Setup – Infrastructure & Access Control 
As described in Annexure B tled “Scope of Works 1 – Founda on Setup – Infrastructure 
& Access Control”, which forms part of this Agreement.










SCOPE OF WORKS 1 - FOUNDATION SETUP – INFRASTRUCTURE & ACCESS CONTROL 
The Contractor will deliver the following technical components, forming the founda onal 
infrastructure of the soware pla orm: 
1. Cloud Infrastructure Setup 
1.1 
Create project environment in Microso Azure under Company’s control. 
1.2 
1.3 
Deploy ini al MySQL database instance. 
Configure ini al CI/CD pipelines for automated deployment and version control. 
2. Database Modelling 
2.1 
Design and document rela onal schema aligned to the Business Requirements Document 
(BRD), covering: 
a) 
Logins and Passwords 
b) User Roles and Permissions 
c) 
Project Setup 
d) Plans and Overlays 
e) 
Fire Penetra on Feature 
f) 
Defects Feature 
3. Authen ca on System 
3.1 
Implement secure email-based user registra on and login. 
3.2 
Enable password reset func onality. 
4. Role-Based Access Control (RBAC) 
4.1 
Super Admin 
Define and implement access levels for the following roles: 
a) 
b) Project Admin 
c) 
Service Provider 
d) Inspector 
e) 
Guest 
4.2 
Permissions must reflect BRD specifica ons. 
5. Basic Admin Panel 
5.1 
Provide a minimal user interface (UI) for user and role management. 
5.2 
Include key Super Admin capabili es such as user crea on, deac va on, and role 
assignment. 
Page 17 of 32 
6. Acceptance Criteria 
The Deliverable will only be accepted upon the following condi ons being fully met: Azure Cloud & 
Database Setup: 
6.1 
6.2 
6.3 
6.4 
6.5 
6.6 
6.7 
6.8 
Azure Cloud & Database Setup 
a) 
Azure project environment is created and accessible. 
b) MySQL database is deployed with appropriate permissions and security 
configura ons. 
c) 
CI/CD pipeline is opera onal and can deploy code from source control to the test 
environment. 
Database Modelling 
a) 
Schema must include tables and rela onships for: user roles, projects, plans, 
overlays, fire penetra on registers (as defined in the BRD). 
b) En ty rela onships must reflect required data integrity (foreign keys, constraints, 
normalisa on). 
Authen ca on System 
a) 
Func onal registra on and login using email/password. 
b) Password recovery via email is tested and secure. 
c) 
Session handling and token storage is secure. 
Role-Based Access Management 
a) 
System supports: Super Admin, Project Admin, Service Provider, Inspector and 
Guest. 
b) Each role must have access only to permi ed screens, ac ons, and data per BRD. 
c) 
Permissions must be enforced both on UI and backend levels. 
Admin Panel (Basic) 
a) 
UI exists (even minimal/basic) for user and role management. 
b) Super Admin can add, update, and deac vate users and assign roles. 
Deployment and Demo 
a) 
Full stack (frontend/backend) runs in Azure test environment. 
b) Contractor provides a walkthrough or demo verifying all func onali es listed above. 
Documenta on 
a) 
Brief deployment and environment instruc ons. 
b) Admin panel user instruc ons (video or text format acceptable). 
Formal Sign-Off 
a) 
Wri en confirma on from the Company that all requirements have been met. 
b) No cri cal defects remain; minor bugs must be documented and scheduled for fix.