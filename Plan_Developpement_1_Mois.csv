<PERSON><PERSON><PERSON>,<PERSON><PERSON>,<PERSON><PERSON><PERSON>,<PERSON><PERSON>,Priorité,<PERSON><PERSON>ut,<PERSON><PERSON>e (heures),Responsable,Dépendances
1,1,Setup Azure Environment,Créer l'environnement de projet Azure sous contrôle de l'entreprise,Haute,À faire,4,<PERSON><PERSON><PERSON>,
1,1,Configure Azure Security,Configurer les permissions et configurations de sécurité Azure,Haute,À faire,3,DevOps,Setup Azure Environment
1,2,Deploy MySQL Database,Déployer l'instance de base de données MySQL initiale,Haute,À faire,4,Backend Dev,Setup Azure Environment
1,2,Database Security Config,Configurer les permissions et sécurité de la base de données,Haute,À faire,2,Backend Dev,Deploy MySQL Database
1,3,Setup CI/CD Pipeline,Configurer les pipelines CI/CD pour déploiement automatisé,Haute,À faire,6,DevOps,Setup Azure Environment
1,3,Version Control Setup,Configurer le contrôle de version et intégration,Moyenne,À faire,2,<PERSON><PERSON><PERSON>,Setup CI/CD Pipeline
1,4,Database Schema Design,Concevoir le schéma relationnel selon BRD,Haute,À faire,6,<PERSON><PERSON> Dev,Deploy MySQL Database
1,4,User Roles Schema,Créer tables pour logins/mots de passe et rôles utilisateurs,Haute,À faire,4,Backend Dev,Database Schema Design
1,5,Project Setup Schema,Créer tables pour configuration des projets,Haute,À faire,3,Backend Dev,Database Schema Design
1,5,Plans and Overlays Schema,Créer tables pour plans et overlays,Moyenne,À faire,3,Backend Dev,Database Schema Design
2,6,Fire Penetration Schema,Créer tables pour fonctionnalité Fire Penetration,Moyenne,À faire,4,Backend Dev,Database Schema Design
2,6,Defects Feature Schema,Créer tables pour gestion des défauts,Moyenne,À faire,3,Backend Dev,Database Schema Design
2,7,Database Relationships,Implémenter les relations entre entités (clés étrangères),Haute,À faire,4,Backend Dev,User Roles Schema
2,7,Data Integrity Constraints,Ajouter contraintes et normalisation,Haute,À faire,3,Backend Dev,Database Relationships
2,8,Authentication Backend,Implémenter système d'authentification email/mot de passe,Haute,À faire,6,Backend Dev,User Roles Schema
2,8,Password Security,Implémenter hachage sécurisé des mots de passe,Haute,À faire,2,Backend Dev,Authentication Backend
2,9,Password Reset Feature,Implémenter fonctionnalité de récupération de mot de passe,Haute,À faire,4,Backend Dev,Authentication Backend
2,9,Email Service Integration,Intégrer service d'email pour récupération mot de passe,Moyenne,À faire,3,Backend Dev,Password Reset Feature
2,10,Session Management,Implémenter gestion des sessions et stockage des tokens,Haute,À faire,4,Backend Dev,Authentication Backend
3,11,RBAC Implementation,Implémenter le contrôle d'accès basé sur les rôles,Haute,À faire,6,Backend Dev,Authentication Backend
3,11,Super Admin Role,Définir et implémenter les accès Super Admin,Haute,À faire,3,Backend Dev,RBAC Implementation
3,12,Project Admin Role,Définir et implémenter les accès Project Admin,Haute,À faire,3,Backend Dev,RBAC Implementation
3,12,Service Provider Role,Définir et implémenter les accès Service Provider,Moyenne,À faire,3,Backend Dev,RBAC Implementation
3,13,Inspector Role,Définir et implémenter les accès Inspector,Moyenne,À faire,3,Backend Dev,RBAC Implementation
3,13,Guest Role,Définir et implémenter les accès Guest,Faible,À faire,2,Backend Dev,RBAC Implementation
3,14,Permission Enforcement,Implémenter l'application des permissions UI et backend,Haute,À faire,5,Full Stack Dev,RBAC Implementation
3,14,API Security Middleware,Créer middleware de sécurité pour APIs,Haute,À faire,4,Backend Dev,Permission Enforcement
3,15,Frontend Setup,Initialiser le projet frontend (React/Vue/Angular),Haute,À faire,4,Frontend Dev,
4,16,Login UI Component,Créer composant d'interface de connexion,Haute,À faire,4,Frontend Dev,Frontend Setup
4,16,Registration UI,Créer interface d'inscription utilisateur,Haute,À faire,3,Frontend Dev,Login UI Component
4,17,Password Reset UI,Créer interface de récupération de mot de passe,Moyenne,À faire,3,Frontend Dev,Registration UI
4,17,Dashboard Layout,Créer layout principal du dashboard,Haute,À faire,4,Frontend Dev,Login UI Component
4,18,Admin Panel UI,Créer interface basique du panel d'administration,Haute,À faire,6,Frontend Dev,Dashboard Layout
4,18,User Management UI,Créer interface de gestion des utilisateurs,Haute,À faire,4,Frontend Dev,Admin Panel UI
4,19,Role Assignment UI,Créer interface d'assignation des rôles,Haute,À faire,4,Frontend Dev,User Management UI
4,19,User Deactivation UI,Créer interface de désactivation des utilisateurs,Moyenne,À faire,2,Frontend Dev,User Management UI
4,20,Frontend-Backend Integration,Intégrer frontend avec APIs backend,Haute,À faire,6,Full Stack Dev,Admin Panel UI
4,21,Authentication Flow Testing,Tester le flux complet d'authentification,Haute,À faire,4,QA/Dev,Frontend-Backend Integration
4,21,RBAC Testing,Tester le système de contrôle d'accès,Haute,À faire,4,QA/Dev,Authentication Flow Testing
4,22,Admin Panel Testing,Tester toutes les fonctionnalités du panel admin,Haute,À faire,4,QA/Dev,RBAC Testing
4,22,Security Testing,Effectuer tests de sécurité (sessions tokens etc),Haute,À faire,3,QA/Dev,Admin Panel Testing
4,23,Azure Deployment,Déployer l'application complète sur Azure,Haute,À faire,4,DevOps,Security Testing
4,23,Environment Testing,Tester l'application dans l'environnement Azure,Haute,À faire,3,QA/Dev,Azure Deployment
4,24,Performance Testing,Effectuer tests de performance et optimisation,Moyenne,À faire,4,QA/Dev,Environment Testing
4,24,Bug Fixes,Corriger les bugs identifiés lors des tests,Haute,À faire,4,Dev Team,Performance Testing
4,25,Documentation Creation,Créer documentation de déploiement et d'environnement,Moyenne,À faire,4,Tech Writer,Bug Fixes
4,25,Admin Instructions,Créer instructions d'utilisation du panel admin,Moyenne,À faire,3,Tech Writer,Documentation Creation
4,26,Demo Preparation,Préparer la démonstration des fonctionnalités,Haute,À faire,3,Project Manager,Admin Instructions
4,26,Walkthrough Demo,Effectuer la démonstration complète du système,Haute,À faire,2,Project Manager,Demo Preparation
4,27,Final Testing,Tests finaux et validation complète,Haute,À faire,4,QA Team,Walkthrough Demo
4,27,Sign-off Preparation,Préparer les documents pour validation finale,Haute,À faire,2,Project Manager,Final Testing
4,28,Client Review,Révision client et feedback,Haute,À faire,4,Client/PM,Sign-off Preparation
4,28,Final Adjustments,Ajustements finaux selon feedback client,Moyenne,À faire,4,Dev Team,Client Review
